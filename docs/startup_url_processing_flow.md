# Startup URL Processing Flow

## Overview

This document describes the system for processing URLs from various metadata sources when the app is opened (first time or brought to foreground) through different entry points that can pass data to the app.

## System Components

### Core Components
- **UrlValidator**: Validates and parses both web URLs (`https://app.appio.so/?s=X&u=Y` or `https://app.appio.so/android/?s=X&u=Y` or `https://app.appio.so/*/?s=X&u=Y`) and deep links (`appio://appio/?service=X&user=Y`) into `ParsedUrlParams(serviceId, customerUserId)`
- **StartupUrlProcessor**: Centralized handler for all URL processing during app startup
- **IntentHandler**: Handles different intent sources (widget, shortcut, deep link, normal launch, other) with URL processing integration
- **InstallReferrer**: Detects and parses install referrer data with service/user parameters, constructs appio:// URLs
- **MainViewModel**: Manages app initialization and intent handling with StartupUrlProcessor integration
- **MainActivity**: Entry point that processes intents via `handleIntent()` and `onNewIntent()` with navigation flow
- **ServiceScreen**: Displays service information when valid URLs are processed
- **QRScanScreen**: QR code scanner that validates URLs and triggers startup processing

### Metadata Entry Points
1. **Install Referrer**: Extracts `service` and `user` parameters, constructs appio:// URLs
2. **Deep Links**: Extracts `intent.data.toString()` and processes via StartupUrlProcessor
3. **Associated Links**: Handled as deep links through Android's app link system
4. **Custom Scheme** (`appio://`): Integrated into startup flow via UrlValidator and StartupUrlProcessor
5. **Widgets**: Looks for `service_id` extra and constructs appio:// URLs
6. **QR Code Scanner**: Validates URLs and triggers startup processing

## Implementation Details

### 1. StartupUrlProcessor Class

**Purpose**: Centralized handler for all URL processing during app startup that decides whether to present ServiceScreen.

**Location**: `app/src/main/java/so/appio/app/utils/StartupUrlProcessor.kt`

**Responsibilities**:
- Parse URLs from various sources using UrlValidator
- Determine if ServiceScreen should be presented
- Handle asynchronous init URL processing logic
- Coordinate with existing app initialization flow

**Methods**:
- `suspend fun processUrl(data: String): URLParams?`

**Return Types**:
```kotlin
data class URLParams(
    val serviceId: String,
    val showPreview: Boolean = false
)
```

**Implementation Details**:
- Method is suspend function for future API calls and database operations
- Init URL processing logic happens ONLY for valid URLs (after UrlValidator success, before returning URLParams)
- Invalid URLs return immediately with null
- Uses existing UrlValidator.validateAndParseUrl() for URL parsing
- Logs using Log.d(TAG, ...) pattern for info, Log.e(TAG, ...) for errors
- customerUserId is only used as input during URL parsing, never in output
- showPreview is true when new Service is detected

### 2. Integration Points

**MainViewModel Integration**:
- StartupUrlProcessor imported and used
- `handleIntent()` delegates to IntentHandler methods that use processor
- `processStartupUrl(url: String, done: () -> Unit)` method available
- App initialization flow coordinates with URL processing

**InstallReferrer Integration**:
- Always uses `appio://appio/?service=X&user=Y` format (or `?service=X` if no user)
- Logs additional parameters but doesn't pass them to processor
- Passes constructed URL string to StartupUrlProcessor.processUrl() via MainViewModel.processStartupUrl()
- Uses callback mechanism with `onValidUrl` lambda

**Widget Integration**:
- Looks ONLY for extra key `service_id` (never `customer_user_id`)
- If `service_id` found, constructs URL: `appio://appio/?service={service_id}`
- Passes constructed URL to processor via MainViewModel.processStartupUrl()

**IntentHandler Integration**:
- `handleDeepLinkIntent()` extracts `intent.data.toString()` and passes to processor
- `handleWidgetIntent()` processes widget metadata and constructs URLs
- All metadata sources are captured and processed
- Each handler calls MainViewModel.processStartupUrl() with appropriate callback

### 3. ServiceScreen Implementation

**Location**: `app/src/main/java/so/appio/app/ui/screens/ServiceScreen.kt`

**ServiceScreen Composable**:
- Screen displays service information
- Accepts serviceId and showPreview parameters ONLY (no customerUserId parameter)
- Shows service information with serviceId in a card
- If showPreview is true, shows "Preview mode"
- No back navigation anywhere. ServiceScreen is the main screen without any option to go back
- ServiceScreen completely replaces IntroScreen flow when shown
- IntroScreen remains available for cases without valid URLs

### 4. State Management

**Initialization Flow**:
- Uses existing urlParams StateFlow for ServiceScreen data
- Splash screen stays visible until URLParams is returned (controlled by isLoading StateFlow)
- App shows either: IntroScreen or ServiceScreen

### 5. Data Flow

**Startup Sequence**:
1. App opens (onCreate/onNewIntent)
2. MainViewModel.handleIntent() called
3. IntentHandler methods extract URLs and call MainViewModel.processStartupUrl()
4. MainViewModel.processStartupUrl() calls StartupUrlProcessor.processUrl() with extracted URL
5. UrlValidator.validateAndParseUrl() validates the URL
6. If Success: [init URL processing logic] → return URLParams(serviceId, showPreview)
7. If Error: return null (no processing delay)
8. MainActivity renders ServiceScreen OR IntroScreen based on URLParams

**App Flow**:
Loading → ServiceScreen (if valid URL) OR IntroScreen (if no valid URL)

**Metadata Processing**:
1. Extract raw metadata from source (intent data, referrer, widget extras, QR scan)
2. Pass metadata to StartupUrlProcessor via MainViewModel.processStartupUrl()
3. StartupUrlProcessor converts string to standardized URL format (if needed)
4. Use UrlValidator.validateAndParseUrl() for validation and parsing inside StartupUrlProcessor
5. Return appropriate URLParams based on validation result

### 6. Error Handling

**Invalid URL Handling**:
- Log invalid URLs using Log.e(TAG, errorMessage) but continue normal app flow
- No disruption to standard app initialization. No exception throwing
- Invalid URLs return null immediately (no init URL processing logic)
- Error fallback goes to MainActivity which shows IntroScreen

**Missing ServiceScreen Data Handling**:
- Ignore any missing metadata and simply continue to IntroScreen
- Log errors using existing Log.e(TAG, ...) pattern

### 7. QR Code Scanner Integration

**Location**: `app/src/main/java/so/appio/app/ui/screens/QRScanScreen.kt`

**QR Scanner Features**:
- Integrated into IntroScreen as secondary navigation option
- Validates scanned URLs using UrlValidator.validateAndParseUrl()
- For valid Appio URLs: triggers StartupUrlProcessor via MainActivity callback
- For valid browser URLs: shows temporary domain label with click-to-open functionality
- Camera permission handling with settings redirect
- Flashlight toggle functionality
- Animated scanning overlay with corner indicators
- Back navigation to IntroScreen

**QR Scanner Flow**:
1. User clicks "Scan the QR code" button in IntroScreen
2. QRScanScreen opens with camera permission request
3. QR code detected and validated via UrlValidator
4. Valid Appio URLs trigger `onValidQRCodeURL` callback to MainActivity
5. MainActivity calls `viewModel.processStartupUrl(url, viewModel::intentDone)`
6. StartupUrlProcessor processes URL and returns URLParams
7. Navigation flows to ServiceScreen if successful

### 8. Future Expansion Points

**Init Logic Expansion**:
- StartupUrlProcessor designed to accommodate additional logic
- Service validation against API
- User authentication checks
- Custom routing based on service type
- A/B testing integration

**Additional Metadata Sources**:
- Push notification payloads
- Shortcut parameters
- Firebase Dynamic Links

### 9. Related Components
- Notification permission flow operates independently
- IntroScreen enhanced with QR scanner integration

## Testing Strategy

**Unit Tests**:
- StartupUrlProcessor logic with various input scenarios
- UrlValidator integration with different metadata formats
- URLParams data class validation
- Error handling scenarios

**Integration Tests**:
- End-to-end flow from intent to ServiceScreen display
- Install referrer processing with appio:// URL construction
- Deep link handling with intent.data extraction
- Widget linking handling with service_id extraction
- QR scanner integration with URL validation

**Manual Testing Scenarios**:
- Test all metadata entry points (install referrer, deep links, custom scheme, widget link, QR scanner)
- Verify ServiceScreen display with correct serviceId
- Verify ServiceScreen display with preview mode
- Test error scenarios and fallbacks to IntroScreen
- Test QR scanner with valid/invalid URLs
- Test camera permissions and flashlight functionality
- Test navigation flows between screens

## Architecture Summary

The startup URL processing system follows this architecture:

**Entry Points** → **Processing** → **Navigation**
1. Install Referrer → InstallReferrer → appio:// URL → StartupUrlProcessor → URLParams → ServiceScreen
2. Deep Links → IntentHandler → URL extraction → StartupUrlProcessor → URLParams → ServiceScreen
3. Widget Clicks → IntentHandler → service_id → appio:// URL → StartupUrlProcessor → URLParams → ServiceScreen
4. QR Scanner → QRScanScreen → URL validation → StartupUrlProcessor → URLParams → ServiceScreen
5. Normal Launch → IntentHandler → No URL → IntroScreen

**State Management**:
- `isLoading`: Controls LoadingScreen visibility during URL processing
- `urlParams`: Contains ServiceScreen data or null for IntroScreen
- Init URL processing logic for valid URLs only

**Error Handling**:
- Invalid URLs return null immediately
- All errors gracefully fall back to IntroScreen
- Comprehensive logging for debugging
