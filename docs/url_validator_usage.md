# UrlValidator Usage Guide

The `UrlValidator` utility class validates and parses Appio URLs to extract service and user parameters.

## Supported URL Formats

1. **Web URL**: `https://app.appio.so/?s=X&u=Y` or `https://app.appio.so/android/?s=X&u=Y` or `https://app.appio.so/*/?s=X&u=Y`
   - `s` parameter = serviceId (required)
   - `u` parameter = customerUserId (optional)

2. **Deep Link**: `appio://appio/?service=X&user=Y`
   - `service` parameter = serviceId (required)
   - `user` parameter = customerUserId (optional)

## Error Handling

The validator returns specific error messages for different validation failures:

- Empty URL: "URL cannot be empty"
- Invalid scheme/host: "Invalid URL format. Expected https://app.appio.so or appio://appio"
- Missing service parameter: "Missing required parameter 's' (serviceId)" or "Missing required parameter 'service' (serviceId)"
- Malformed URL: "Failed to parse URL: [exception message]"
