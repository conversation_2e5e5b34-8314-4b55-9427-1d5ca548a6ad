package so.appio.app.ui.screens

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Refresh
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.input.nestedscroll.nestedScroll
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import so.appio.app.data.database.DatabaseManager
import so.appio.app.data.entity.notification.Notification
import so.appio.app.data.entity.service.Service
import so.appio.app.ui.theme.AppioAppTheme
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ServiceScreen(
    modifier: Modifier = Modifier,
    service: Service,
) {
    val context = LocalContext.current
    val notificationRepository = remember { DatabaseManager.getNotificationRepository() }
    val notifications by notificationRepository.getNotificationsByService(service.id).collectAsState(initial = emptyList())
    val scrollBehavior = TopAppBarDefaults.pinnedScrollBehavior()

    Scaffold(
        modifier = modifier
            .fillMaxSize()
            .nestedScroll(scrollBehavior.nestedScrollConnection),
        topBar = {
            TopAppBar(
                title = {
                    Text(
                        text = service.title,
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis
                    )
                },
                actions = {
                    IconButton(
                        onClick = {
                            // TODO: Implement refresh functionality
                            println("TODO: Implement notification refresh")
                        }
                    ) {
                        Icon(
                            imageVector = Icons.Default.Refresh,
                            contentDescription = "Refresh notifications"
                        )
                    }
                },
                scrollBehavior = scrollBehavior
            )
        }
    ) { innerPadding ->
        ServiceContent(
            notifications = notifications,
            modifier = Modifier.padding(innerPadding)
        )
    }
}

@Composable
private fun ServiceContent(
    notifications: List<Notification>,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier
            .fillMaxSize()
            .background(MaterialTheme.colorScheme.background)
    ) {
        // Notifications list
        LazyColumn(
            modifier = Modifier
                .weight(1f)
                .fillMaxWidth()
        ) {
            items(notifications) { notification ->
                NotificationItem(notification = notification)
                HorizontalDivider()
            }
        }

        // Footer with notification count
        ServiceFooter(notificationCount = notifications.size)
    }
}



@Composable
private fun NotificationItem(
    notification: Notification,
    modifier: Modifier = Modifier
) {
    ListItem(
        modifier = modifier,
        headlineContent = {
            Text(
                text = notification.title,
                maxLines = 1,
                overflow = TextOverflow.Ellipsis
            )
        },
        supportingContent = {
            Text(
                text = notification.body,
                maxLines = 1,
                overflow = TextOverflow.Ellipsis
            )
        },
        trailingContent = {
            Text(
                text = formatDate(notification.receivedAt),
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
    )
}

@Composable
private fun ServiceFooter(
    notificationCount: Int,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier.fillMaxWidth()
    ) {
        HorizontalDivider()
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            contentAlignment = Alignment.Center
        ) {
            Text(
                text = "$notificationCount notifications",
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurface
            )
        }
    }
}

private fun formatDate(date: Date): String {
    val formatter = SimpleDateFormat("MMM dd\nHH:mm", Locale.getDefault())
    return formatter.format(date)
}

@Preview(showBackground = true)
@Composable
fun ServiceScreenPreview() {
    AppioAppTheme {
        ServiceScreen(
            service = Service(
                id = "svc_demo_123456789",
                title = "Demo Service",
                description = "This is a demo service for testing purposes",
                logoURL = "https://example.com/logo.png",
                showPreview = false,
                lastUpdate = Date(),
                lastSync = Date()
            )
        )
    }
}

@Preview(showBackground = true)
@Composable
fun ServiceScreenPreviewModePreview() {
    AppioAppTheme {
        ServiceScreen(
            service = Service(
                id = "svc_demo_123456789",
                title = "Demo Service",
                description = "This is a demo service with preview mode enabled",
                logoURL = "https://example.com/logo.png",
                showPreview = true,
                lastUpdate = Date(),
                lastSync = Date()
            )
        )
    }
}

@Preview(showBackground = true, uiMode = android.content.res.Configuration.UI_MODE_NIGHT_YES)
@Composable
fun ServiceScreenDarkPreview() {
    AppioAppTheme {
        ServiceScreen(
            service = Service(
                id = "svc_demo_123456789",
                title = "Demo Service",
                description = "Dark mode preview of the service screen",
                logoURL = "https://example.com/logo.png",
                showPreview = false,
                lastUpdate = Date(),
                lastSync = Date()
            )
        )
    }
}
