package so.appio.app.data.entity.widget

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

/**
 * Response models for widget-related API operations
 */

@Serializable
data class WidgetResponse(
    @SerialName("id")
    val id: String,

    @SerialName("service_id")
    val serviceId: String,

    @SerialName("name")
    val name: String,

    @SerialName("config")
    val config: String
)
