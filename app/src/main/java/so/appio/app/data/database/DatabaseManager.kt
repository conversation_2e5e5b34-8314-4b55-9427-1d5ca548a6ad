package so.appio.app.data.database

import android.content.Context
import android.util.Log
import so.appio.app.data.repository.NotificationRepository
import so.appio.app.data.repository.ServiceRepository
import so.appio.app.data.repository.WidgetRepository

/**
 * Database manager that provides centralized access to database components.
 * 
 * This singleton manages the database instance and provides repositories
 * for easy access throughout the application.
 */
object DatabaseManager {
    private const val TAG = "LOG:DatabaseManager"
    
    private var database: AppDatabase? = null
    private var serviceRepository: ServiceRepository? = null
    private var notificationRepository: NotificationRepository? = null
    private var widgetRepository: WidgetRepository? = null
    private var appContext: Context? = null
    
    /**
     * Initialize the database manager with application context.
     * This should be called once during app startup.
     */
    fun initialize(context: Context) {
        try {
            appContext = context.applicationContext
            database = AppDatabase.getDatabase(appContext!!)
            serviceRepository = ServiceRepository(database!!.serviceDao())
            notificationRepository = NotificationRepository(database!!.notificationDao())
            widgetRepository = WidgetRepository(database!!.widgetDao())

            Log.d(TAG, "DatabaseManager initialized successfully")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to initialize DatabaseManager", e)
            throw e
        }
    }
    
    /**
     * Get the Service repository instance.
     * Throws IllegalStateException if not initialized.
     */
    fun getServiceRepository(): ServiceRepository {
        return serviceRepository ?: throw IllegalStateException(
            "DatabaseManager not initialized. Call initialize() first."
        )
    }

    /**
     * Get the Notification repository instance.
     * Throws IllegalStateException if not initialized.
     */
    fun getNotificationRepository(): NotificationRepository {
        return notificationRepository ?: throw IllegalStateException(
            "DatabaseManager not initialized. Call initialize() first."
        )
    }

    /**
     * Get the Widget repository instance.
     * Throws IllegalStateException if not initialized.
     */
    fun getWidgetRepository(): WidgetRepository {
        return widgetRepository ?: throw IllegalStateException(
            "DatabaseManager not initialized. Call initialize() first."
        )
    }

    /**
     * Get the database instance.
     * Throws IllegalStateException if not initialized.
     */
    fun getDatabase(): AppDatabase {
        return database ?: throw IllegalStateException(
            "DatabaseManager not initialized. Call initialize() first."
        )
    }
    
    /**
     * Check if the database manager is initialized
     */
    fun isInitialized(): Boolean {
        return database != null && serviceRepository != null && notificationRepository != null && widgetRepository != null && appContext != null
    }
    
    /**
     * Clear all instances (useful for developing)
     */
    fun clearInstances() {
        database = null
        serviceRepository = null
        notificationRepository = null
        widgetRepository = null
        appContext = null
        AppDatabase.clearInstance()
        Log.d(TAG, "DatabaseManager instances cleared")
    }
}
