package so.appio.app.data.entity.widget

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey
import java.util.Date

/**
 * Widget entity representing a widget stored in the Room database.
 * 
 * @param id Unique identifier for the widget (not auto-generated)
 * @param serviceId ID of the service this widget belongs to
 * @param name Widget name
 * @param config Widget configuration as JSON string
 * @param updatedAt Last update timestamp, defaults to current time
 */
@Entity(tableName = "widgets")
data class Widget(
    @PrimaryKey
    @ColumnInfo(name = "id")
    val id: String,
    
    @ColumnInfo(name = "service_id")
    val serviceId: String,
    
    @ColumnInfo(name = "name")
    val name: String,
    
    @ColumnInfo(name = "config")
    val config: String,
    
    @ColumnInfo(name = "updated_at")
    val updatedAt: Date = Date()
)
