package so.appio.app.data.dao

import androidx.room.Dao
import androidx.room.Delete
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Update
import kotlinx.coroutines.flow.Flow
import so.appio.app.data.entity.widget.Widget

/**
 * Data Access Object (DAO) for Widget entity.
 * Provides methods to interact with the widgets table in the database.
 */
@Dao
interface WidgetDao {

    /**
     * Get widgets for a specific service as a Flow
     */
    @Query("SELECT * FROM widgets WHERE service_id = :serviceId ORDER BY id ASC")
    fun getWidgetsByService(serviceId: String): Flow<List<Widget>>
    
    /**
     * Get widgets for a specific service as a list
     */
    @Query("SELECT * FROM widgets WHERE service_id = :serviceId ORDER BY id ASC")
    suspend fun getWidgetsByServiceList(serviceId: String): List<Widget>
    
    /**
     * Get a widget by its ID
     */
    @Query("SELECT * FROM widgets WHERE id = :widgetId")
    suspend fun getWidgetById(widgetId: String): Widget?
    
    /**
     * Get a widget by its ID as Flow for reactive updates
     */
    @Query("SELECT * FROM widgets WHERE id = :widgetId")
    fun getWidgetByIdFlow(widgetId: String): Flow<Widget?>
    
    /**
     * Insert a new widget. Replace if conflict occurs.
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertWidget(widget: Widget)
    
    /**
     * Insert multiple widgets. Replace if conflict occurs.
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertWidgets(widgets: List<Widget>)
    
    /**
     * Update an existing widget
     */
    @Update
    suspend fun updateWidget(widget: Widget)
    
    /**
     * Delete a widget
     */
    @Delete
    suspend fun deleteWidget(widget: Widget)
    
    /**
     * Delete a widget by its ID
     */
    @Query("DELETE FROM widgets WHERE id = :widgetId")
    suspend fun deleteWidgetById(widgetId: String)
    
    /**
     * Delete all widgets for a specific service
     */
    @Query("DELETE FROM widgets WHERE service_id = :serviceId")
    suspend fun deleteWidgetsByService(serviceId: String)
    
    /**
     * Get count of all widgets
     */
    @Query("SELECT COUNT(*) FROM widgets")
    suspend fun getWidgetCount(): Int
    
    /**
     * Get count of widgets for a specific service
     */
    @Query("SELECT COUNT(*) FROM widgets WHERE service_id = :serviceId")
    suspend fun getWidgetCountByService(serviceId: String): Int
    
    /**
     * Check if a widget exists by ID
     */
    @Query("SELECT EXISTS(SELECT 1 FROM widgets WHERE id = :widgetId)")
    suspend fun widgetExists(widgetId: String): Boolean
}
