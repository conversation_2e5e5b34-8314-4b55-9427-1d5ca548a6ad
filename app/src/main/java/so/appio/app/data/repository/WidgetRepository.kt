package so.appio.app.data.repository

import kotlinx.coroutines.flow.Flow
import so.appio.app.data.dao.WidgetDao
import so.appio.app.data.entity.widget.Widget

/**
 * Repository for Widget entity.
 * 
 * Provides a clean API for accessing Widget data and abstracts the data source.
 * This repository currently uses Room database but could be extended to include
 * network calls, caching strategies, etc.
 */
class WidgetRepository(private val widgetDao: WidgetDao) {
    
    /**
     * Get widgets for a specific service as a Flow
     */
    fun getWidgetsByService(serviceId: String): Flow<List<Widget>> = widgetDao.getWidgetsByService(serviceId)
    
    /**
     * Get widgets for a specific service as a list
     */
    suspend fun getWidgetsByServiceList(serviceId: String): List<Widget> = widgetDao.getWidgetsByServiceList(serviceId)
    
    /**
     * Get a widget by its ID
     */
    suspend fun getWidgetById(widgetId: String): Widget? = widgetDao.getWidgetById(widgetId)
    
    /**
     * Get a widget by its ID as Flow for reactive updates
     */
    fun getWidgetByIdFlow(widgetId: String): Flow<Widget?> = widgetDao.getWidgetByIdFlow(widgetId)
    
    /**
     * Insert a new widget
     */
    suspend fun insertWidget(widget: Widget) = widgetDao.insertWidget(widget)
    
    /**
     * Insert multiple widgets
     */
    suspend fun insertWidgets(widgets: List<Widget>) = widgetDao.insertWidgets(widgets)
    
    /**
     * Update an existing widget
     */
    suspend fun updateWidget(widget: Widget) = widgetDao.updateWidget(widget)
    
    /**
     * Delete a widget
     */
    suspend fun deleteWidget(widget: Widget) = widgetDao.deleteWidget(widget)
    
    /**
     * Delete a widget by its ID
     */
    suspend fun deleteWidgetById(widgetId: String) = widgetDao.deleteWidgetById(widgetId)
    
    /**
     * Delete all widgets for a specific service
     */
    suspend fun deleteWidgetsByService(serviceId: String) = widgetDao.deleteWidgetsByService(serviceId)
    
    /**
     * Get count of all widgets
     */
    suspend fun getWidgetCount(): Int = widgetDao.getWidgetCount()
    
    /**
     * Get count of widgets for a specific service
     */
    suspend fun getWidgetCountByService(serviceId: String): Int = widgetDao.getWidgetCountByService(serviceId)
    
    /**
     * Check if a widget exists by ID
     */
    suspend fun widgetExists(widgetId: String): Boolean = widgetDao.widgetExists(widgetId)
}
