package so.appio.app.network

import okhttp3.OkHttpClient
import java.util.concurrent.TimeUnit

/**
 * Shared HTTP client configuration for consistent network behavior across the app.
 * Used by both APIClient and Glide's AppGlideModule.
 */
object HttpClientConfig {
    
    /**
     * Creates a configured OkHttpClient with standard timeouts and settings.
     * This configuration is shared between API calls and image loading.
     */
    fun createClient(): OkHttpClient {
        return OkHttpClient.Builder()
            .connectTimeout(5, TimeUnit.SECONDS)
            .readTimeout(5, TimeUnit.SECONDS)
            .writeTimeout(5, TimeUnit.SECONDS)
//            .retryOnConnectionFailure(true)
//            .followRedirects(true)
//            .followSslRedirects(true)
            .build()
    }
}
