package so.appio.app.widgets

import android.content.Context
import androidx.glance.GlanceId
import androidx.glance.action.ActionParameters
import androidx.glance.appwidget.action.ActionCallback
import android.util.Log
import kotlin.random.Random
import androidx.compose.ui.graphics.toArgb
import androidx.glance.appwidget.state.updateAppWidgetState

class WidgetRefreshAction : ActionCallback {

    companion object {
        // TODO: change name for param
        // val valKey = ActionParameters.Key<String>("value_key")
    }

    override suspend fun onAction(
        context: Context,
        glanceId: GlanceId,
        parameters: ActionParameters,
    ) {
        // val value = parameters[valKey]
        Log.d("LOG:WidgetRefreshAction", "Refreshing widget")

        // Update widget state (for demo purposes - remove when real data is implemented)
        updateAppWidgetState(context, glanceId) { prefs ->
            val randomColor = androidx.compose.ui.graphics.Color(
                red = Random.nextFloat(),
                green = Random.nextFloat(),
                blue = Random.nextFloat(),
                alpha = 1.0f
            )
            prefs[AppioWidget.KEY_BG] = randomColor.toArgb()
        }

        AppioWidgetUpdateWorker.updateWidgetsForEvent(
            context,
            AppioWidgetUpdateWorker.REASON_MANUAL_REFRESH
        )
    }
}