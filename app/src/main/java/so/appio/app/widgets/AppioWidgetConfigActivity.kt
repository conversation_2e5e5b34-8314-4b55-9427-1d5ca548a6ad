package so.appio.app.widgets

import android.appwidget.AppWidgetManager
import android.os.Bundle
import android.util.Log
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.toArgb
import androidx.lifecycle.lifecycleScope
import kotlinx.coroutines.launch
import so.appio.app.ui.theme.AppioAppTheme
import android.content.Intent
import androidx.activity.enableEdgeToEdge
import androidx.glance.appwidget.GlanceAppWidgetManager
import androidx.glance.appwidget.state.updateAppWidgetState
import so.appio.app.ui.widgets.WidgetConfigScreen

class AppioWidgetConfigActivity : ComponentActivity() {

    companion object {
        private const val TAG = "LOG:AppioWidgetConfigActivity"
    }

    // TODO: read stored values. Widget can be re-configured
    override fun onCreate(savedInstanceState: Bundle?) {

        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        
        // Find the widget id from the intent
        val appWidgetId = intent?.extras?.getInt(
            AppWidgetManager.EXTRA_APPWIDGET_ID,
            AppWidgetManager.INVALID_APPWIDGET_ID
        ) ?: AppWidgetManager.INVALID_APPWIDGET_ID

        // If this activity was started with an intent without an app widget ID, finish with an error.
        if (appWidgetId == AppWidgetManager.INVALID_APPWIDGET_ID) {
            Log.e(TAG, "Invalid app widget ID")
            finish()
            return
        }

        // If the user backs out of the activity before reaching the end, the system notifies the
        // app widget host that the configuration is canceled and the host doesn't add the widget
        val resultValue = Intent().putExtra(AppWidgetManager.EXTRA_APPWIDGET_ID, appWidgetId)
        setResult(RESULT_CANCELED, resultValue)

        Log.d(TAG, "Configuring widget with ID: $appWidgetId")

        setContent {
            AppioAppTheme {
                Surface(
                    modifier = Modifier.fillMaxSize(),
                    color = MaterialTheme.colorScheme.background
                ) {
                    WidgetConfigScreen(
                        onColorSelected = { color ->
                            saveConfiguration(appWidgetId, color)
                        }
                    )
                }
            }
        }
    }

    private fun saveConfiguration(appWidgetId: Int, selectedColor: Color) {
        lifecycleScope.launch {
            try {
                val context = this@AppioWidgetConfigActivity
                val glanceManager = GlanceAppWidgetManager(context)
                val glanceId = glanceManager.getGlanceIdBy(appWidgetId)

                // Save the selected color via glance DataStore
                updateAppWidgetState(context, glanceId) { prefs ->
                    prefs[AppioWidget.KEY_BG] = selectedColor.toArgb()
                }

                // IMMEDIATE DATA FETCH: Trigger immediate data fetch for this new widget
                AppioWidgetUpdateWorker.updateWidgetsForEvent(
                    context,
                    AppioWidgetUpdateWorker.REASON_CONFIG_SAVED
                )

                // Update single widget
                AppioWidget().update(context, glanceId)
                Log.d(TAG, "Called update for widget: $glanceId")

//                // Update all Glance widgets to reflect the configuration change
//                AppioWidget().updateAll(context)
//                Log.d(TAG, "Called updateAll() for all widgets")

                val resultValue = Intent().putExtra(
                    AppWidgetManager.EXTRA_APPWIDGET_ID,
                    appWidgetId,
                )
                setResult(RESULT_OK, resultValue)
                finish()
            } catch (e: Exception) {
                Log.e(TAG, "Error configuring widget", e)
                setResult(RESULT_CANCELED)
                finish()
            }
        }
    }
}


