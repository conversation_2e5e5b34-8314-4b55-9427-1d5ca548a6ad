package so.appio.app.widgets

import android.content.Context
import android.util.Log
import androidx.glance.appwidget.updateAll
import androidx.glance.appwidget.state.updateAppWidgetState
import androidx.work.CoroutineWorker
import androidx.work.OneTimeWorkRequestBuilder
import androidx.work.PeriodicWorkRequestBuilder
import androidx.work.WorkManager
import androidx.work.WorkerParameters
import androidx.work.Data
import androidx.work.ExistingWorkPolicy
import androidx.work.ExistingPeriodicWorkPolicy
import java.util.concurrent.TimeUnit

class AppioWidgetUpdateWorker(
    context: Context,
    workerParams: WorkerParameters
) : CoroutineWorker(context, workerParams) {

    companion object {
        private const val TAG = "LOG:AppioWidgetUpdateWorker"
        private const val UPDATE_INTERVAL_MINUTES = 5L // Update every 5 minutes
        private const val ONE_TIME_WORK_NAME = "AppioWidgetUpdate"
        private const val PERIODIC_WORK_NAME = "AppioWidgetPeriodicUpdate"
        private const val KEY_UPDATE_REASON = "update_reason"

        // Update reasons for debugging and analytics
        const val REASON_NOTIFICATION_RECEIVED = "notification_received"
        const val REASON_CONFIG_SAVED = "config_saved"
        const val REASON_MANUAL_REFRESH = "manual_refresh"
        const val REASON_PERIODIC = "periodic"

        /**
         * Start continuous updates (main update strategy)
         */
        fun startAutoUpdate(context: Context) {
            Log.d(TAG, "Starting widget auto-update (every $UPDATE_INTERVAL_MINUTES minutes)")
            val inputData = Data.Builder()
                .putString(KEY_UPDATE_REASON, REASON_PERIODIC)
                .build()

            val periodicWorkRequest = PeriodicWorkRequestBuilder<AppioWidgetUpdateWorker>(
                UPDATE_INTERVAL_MINUTES, TimeUnit.MINUTES
            )
                .setInputData(inputData)
                .addTag(PERIODIC_WORK_NAME)
                .build()

            WorkManager.getInstance(context).enqueueUniquePeriodicWork(
                PERIODIC_WORK_NAME,
                ExistingPeriodicWorkPolicy.KEEP, // Keep existing if already running
                periodicWorkRequest
            )
        }

        /**
         * Trigger immediate widget update for specific events (bonus responsiveness)
         */
        fun updateWidgetsForEvent(context: Context, reason: String) {
            Log.d(TAG, "Triggering immediate widget update for reason: $reason")

            val inputData = Data.Builder()
                .putString(KEY_UPDATE_REASON, reason)
                .build()

            val workRequest = OneTimeWorkRequestBuilder<AppioWidgetUpdateWorker>()
                .setInputData(inputData)
                .addTag(ONE_TIME_WORK_NAME)
                .build()

            // Use REPLACE to avoid duplicate immediate work, but don't interfere with periodic updates
            WorkManager.getInstance(context).enqueueUniqueWork(
                ONE_TIME_WORK_NAME,
                ExistingWorkPolicy.REPLACE,
                workRequest
            )
        }

        fun stopAutoUpdate(context: Context) {
            Log.d(TAG, "Stopping widget auto-update")
            // Cancel the periodic updates
            WorkManager.getInstance(context).cancelUniqueWork(PERIODIC_WORK_NAME)
            // Cancel any pending immediate event-driven updates
            WorkManager.getInstance(context).cancelUniqueWork(ONE_TIME_WORK_NAME)
        }
    }

    override suspend fun doWork(): Result {
        val updateReason = inputData.getString(KEY_UPDATE_REASON) ?: REASON_PERIODIC
        Log.d(TAG, "Updating all widgets for reason: $updateReason")

        // Fetch data and update all widgets
        val apiData = WidgetManager.fetchData()
        updateAllWidgetsWithData(apiData)

        return Result.success()
    }

    private suspend fun updateAllWidgetsWithData(apiData: String) {
        val glanceManager = androidx.glance.appwidget.GlanceAppWidgetManager(applicationContext)
        val glanceIds = glanceManager.getGlanceIds(AppioWidget::class.java)

        // Store API data in each widget's state
        glanceIds.forEach { glanceId ->
            updateAppWidgetState(applicationContext, glanceId) { prefs ->
                prefs[AppioWidget.KEY_API_DATA] = apiData
            }
        }

        // Update all widgets UI
        AppioWidget().updateAll(applicationContext)
        Log.d(TAG, "All widgets updated with API data: $apiData")
    }
}