package so.appio.app.widgets

import android.content.Context
import android.util.Log
import androidx.datastore.preferences.core.intPreferencesKey
import androidx.datastore.preferences.core.stringPreferencesKey
import androidx.glance.GlanceId
import androidx.glance.GlanceTheme
import androidx.glance.LocalSize
import androidx.glance.appwidget.GlanceAppWidget
import androidx.glance.appwidget.GlanceAppWidgetManager
import androidx.glance.appwidget.SizeMode
import androidx.glance.appwidget.provideContent
import androidx.glance.currentState
import so.appio.app.ui.widgets.WidgetContent
import so.appio.app.ui.widgets.ZeroStateContent
import so.appio.app.R

class AppioWidget : GlanceAppWidget(errorUiLayout = R.layout.widget_error_layout) {

    // Use SizeMode.Exact for real-time size detection
    override val sizeMode = SizeMode.Exact

    companion object {
        private const val TAG = "LOG:AppioWidget"

        val KEY_BG = intPreferencesKey("backgroundColor")
        val KEY_API_DATA = stringPreferencesKey("apiData")
    }

//    // Custom error processing on failure. Already showing widget_error_layout
//    override fun onCompositionError(
//        context: Context,
//        glanceId: GlanceId,
//        appWidgetId: Int,
//        throwable: Throwable
//    ) {
//        super.onCompositionError(context, glanceId, appWidgetId, throwable)
//    }

    override suspend fun provideGlance(context: Context, id: GlanceId) {
        Log.d(TAG, "Providing glance for widget id $id")

        // Note: this might not be needed
        val glanceAppWidgetManager = GlanceAppWidgetManager(context)
        val appWidgetId = try {
            glanceAppWidgetManager.getAppWidgetId(id)
        } catch (e: Exception) {
            Log.e(TAG, "Error getting app widget ID", e)
            0
        }

        // Only for UI composition and rendering cached data
        // Data fetching is in AppioWidgetUpdateWorker.doWork()
        provideContent {
            val configColor = currentState(KEY_BG)
            val apiData = currentState(KEY_API_DATA)
            val isConfigured = configColor != null && configColor != 0
            Log.d(TAG, "Widget $appWidgetId has color: $configColor and isConfigured: $isConfigured")

            val size = LocalSize.current
            Log.d(TAG, "Widget $appWidgetId size: $size")

            GlanceTheme {
                if (isConfigured) {
                    Log.d(TAG, "Showing configured content for widget $appWidgetId")
                    WidgetContent(context = context, glanceId = id, apiData = apiData)
                } else {
                    Log.d(TAG, "Showing zero state for widget $appWidgetId")
                    ZeroStateContent(context = context, appWidgetId = appWidgetId)
                }
            }
        }
    }
}


