package so.appio.app

import android.app.Application
import android.util.Log
import so.appio.app.MainActivity.Companion.TAG
import so.appio.app.data.database.DatabaseManager
import so.appio.app.network.APIClient
import so.appio.app.utils.GooglePlayServices
import so.appio.app.utils.ImageCacheManager
import so.appio.app.utils.NotificationChannels

class MyApplication : Application() {

    override fun onCreate() {
        super.onCreate()
        Log.d(TAG, "onCreate called")

        // Initialize core services
        GooglePlayServices.initializeFirebase(this)
        NotificationChannels.createNotificationChannels(this)
        ImageCacheManager.initialize(this)
        DatabaseManager.initialize(this)
        APIClient.initialize(this)
    }
}