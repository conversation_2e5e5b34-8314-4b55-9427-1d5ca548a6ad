package so.appio.app.utils

import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.drawable.BitmapDrawable
import android.util.Log
import androidx.core.content.ContextCompat
import androidx.core.graphics.createBitmap
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.request.RequestOptions
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import so.appio.app.R
import java.io.File
import java.io.FileOutputStream
import java.io.IOException
import java.security.MessageDigest
import java.util.concurrent.TimeUnit

/**
 * Manages persistent caching of images to avoid network requests during usage.
 * Images are stored persistently in the app's cache directory and survive app restarts.
 */
object ImageCacheManager {
    private const val TAG = "LOG:ImageCacheManager"
    private const val CACHE_DIRECTORY_NAME = "image_cache"

    // Application context for operations
    private var appContext: Context? = null

    /**
     * Initialize the ImageCacheManager with application context
     * This should be called once during app startup
     */
    fun initialize(context: Context) {
        appContext = context.applicationContext
        Log.d(TAG, "ImageCacheManager initialized successfully")
    }

    /**
     * Safely get the application context, logging a warning if not initialized
     * @return The application context, or null if not initialized
     */
    private fun getContextSafely(): Context? {
        return appContext ?: run {
            Log.w(TAG, "ImageCacheManager not initialized. Call initialize() first.")
            null
        }
    }

    /**
     * Pre-cache an image from the given URL to persistent storage without returning anything
     * @param url The URL of the image to cache persistently
     * @param force If true, re-download even if cached; if false, skip if already cached
     */
    suspend fun preCacheImage(url: String, force: Boolean = false) {
        getCachedImage(url, force)
    }

    /**
     * Get a cached image from the given URL, downloading and storing persistently if needed
     * @param url The URL of the image to cache
     * @param force If true, re-download even if cached; if false, return cached version if available
     * @return The cached or downloaded bitmap, or a placeholder if download fails
     */
    suspend fun getCachedImage(url: String, force: Boolean = false): Bitmap {
        return withContext(Dispatchers.IO) {
            val context = getContextSafely()
            if (context == null) {
                Log.w(TAG, "Cannot cache image from URL $url - ImageCacheManager not initialized")
                return@withContext getPlaceholderBitmapFallback()
            }

            try {
                val hashedFilename = generateHashedFilename(url)

                // Check persistent file cache
                val cachedFile = getCachedImageFile(context, hashedFilename)
                if (!force && cachedFile.exists()) {
                    Log.d(TAG, "Loading image from persistent cache: $hashedFilename")
                    val bitmap = BitmapFactory.decodeFile(cachedFile.absolutePath)
                    if (bitmap != null) {
                        Log.d(TAG, "Successfully loaded image from persistent cache")
                        return@withContext bitmap
                    } else {
                        Log.w(TAG, "Cached file exists but couldn't decode bitmap, will re-download")
                        cachedFile.delete()
                    }
                }

                // Check network connectivity before attempting download
                if (!NetworkUtils.isNetworkAvailable(context)) {
                    Log.w(TAG, "No network connection available for downloading image from URL: $url")
                    return@withContext getPlaceholderBitmap(context)
                }

                // Download image with improved Glide configuration
                 Log.d(TAG, "Downloading image from URL: $url")
                val requestOptions = RequestOptions()
                    .diskCacheStrategy(DiskCacheStrategy.DATA)
                    .timeout(15000) // 15 second timeout

                val bitmap = Glide.with(context)
                    .asBitmap()
                    .load(url)
                    .apply(requestOptions)
                    .submit()
                    .get(15, TimeUnit.SECONDS)

                if (bitmap != null) {
                    // Cache the bitmap to persistent storage
                    saveBitmapToCache(context, bitmap, hashedFilename)
                    Log.d(TAG, "Successfully downloaded and cached image persistently")
                    return@withContext bitmap
                } else {
                    Log.e(TAG, "Downloaded bitmap is null")
                    return@withContext getPlaceholderBitmap(context)
                }

            } catch (e: Exception) {
                Log.e(TAG, "Failed to cache image from URL $url: ${e.message}", e)
                return@withContext getPlaceholderBitmap(context)
            }
        }
    }

    /**
     * Clear cached image for a specific URL
     * @param url The URL of the image to clear from cache
     */
    fun clearCache(url: String) {
        val context = getContextSafely()
        if (context == null) {
            Log.w(TAG, "Cannot clear cache for URL $url - ImageCacheManager not initialized")
            return
        }

        try {
            val hashedFilename = generateHashedFilename(url)

            // Remove from file cache
            val cachedFile = getCachedImageFile(context, hashedFilename)
            if (cachedFile.exists()) {
                cachedFile.delete()
                Log.d(TAG, "Cleared cache for URL: $url")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Failed to clear cache for URL $url: ${e.message}", e)
        }
    }

    /**
     * Clear all cached images from persistent storage
     */
    fun clearAllCache() {
        val context = getContextSafely()
        if (context == null) {
            Log.w(TAG, "Cannot clear all cache - ImageCacheManager not initialized")
            return
        }

        try {
            val imageCacheDir = File(context.filesDir, CACHE_DIRECTORY_NAME)

            // Simply delete the entire directory and recreate it
            if (imageCacheDir.exists()) {
                imageCacheDir.deleteRecursively()
            }

            // Recreate the directory
            imageCacheDir.mkdirs()

            Log.d(TAG, "Cleared all persistent image cache")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to clear all cache: ${e.message}", e)
        }
    }

    /**
     * Generate a consistent filename from URL using MD5 hash
     */
    private fun generateHashedFilename(url: String): String {
        val md5 = MessageDigest.getInstance("MD5")
        val hashBytes = md5.digest(url.toByteArray())
        val hashString = hashBytes.joinToString("") { "%02x".format(it) }
        return "$hashString.png"
    }

    /**
     * Get the dedicated directory for persistent image storage
     * Images are stored in: /data/data/so.appio.app/files/image_cache/
     * This directory persists until app uninstall (not cleared by system or cache clearing)
     */
    private fun getImageCacheDir(context: Context): File {
        val cacheDir = File(context.filesDir, CACHE_DIRECTORY_NAME)
        if (!cacheDir.exists()) {
            cacheDir.mkdirs()
        }
        return cacheDir
    }

    /**
     * Get the cached image file for a given hashed filename
     */
    private fun getCachedImageFile(context: Context, hashedFilename: String): File {
        return File(getImageCacheDir(context), hashedFilename)
    }

    /**
     * Save bitmap to cache with the given filename
     */
    private fun saveBitmapToCache(context: Context, bitmap: Bitmap, hashedFilename: String) {
        try {
            val cacheFile = getCachedImageFile(context, hashedFilename)
            FileOutputStream(cacheFile).use { out ->
                bitmap.compress(Bitmap.CompressFormat.PNG, 100, out)
            }
            Log.d(TAG, "Bitmap saved to cache: ${cacheFile.absolutePath}")
        } catch (e: IOException) {
            Log.e(TAG, "Failed to save bitmap to cache: ${e.message}", e)
        }
    }

    /**
     * Get a placeholder bitmap from the app's default icon
     */
    private fun getPlaceholderBitmap(context: Context): Bitmap {
        return try {
            val drawable = ContextCompat.getDrawable(context, R.drawable.ic_app)
            if (drawable is BitmapDrawable) {
                drawable.bitmap
            } else {
                // Convert vector drawable to bitmap with 20 points padding
                val originalWidth = drawable!!.intrinsicWidth
                val originalHeight = drawable.intrinsicHeight
                val padding = originalWidth / 2

                val bitmap = createBitmap(originalWidth * 2, originalHeight * 2)
                val canvas = Canvas(bitmap)

                // Draw the drawable centered with padding
                drawable.setBounds(padding, padding, padding + originalWidth, padding + originalHeight)
                drawable.draw(canvas)
                bitmap
            }
        } catch (e: Exception) {
            Log.e(TAG, "Failed to create placeholder bitmap: ${e.message}", e)
            getPlaceholderBitmapFallback()
        }
    }

    /**
     * Get a fallback placeholder bitmap when context is not available
     * Creates a simple gray bitmap as ultimate fallback
     */
    private fun getPlaceholderBitmapFallback(): Bitmap {
        Log.d(TAG, "Creating fallback placeholder bitmap")
        return createBitmap(100, 100).apply {
            eraseColor(Color.LTGRAY)
        }
    }
}
