package so.appio.app.utils

import android.content.Context
import android.util.Log
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

class FingerPrintManager(
    private val context: Context
) {

    companion object {
        private const val TAG = "LOG:FingerPrintManager"
    }

    private val deviceManager = DeviceManager(context)

    /**
     * Check for fingerprint match and register device if found
     */
    suspend fun check() = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Starting fingerprint check")
            
            val fingerprintResponse = deviceManager.matchFingerprint()
            if (fingerprintResponse == null) {
                Log.d(TAG, "No fingerprint match found")
                return@withContext
            }

            if (fingerprintResponse.serviceId.isEmpty() || fingerprintResponse.customerUserId.isEmpty()) {
                Log.d(TAG, "Fingerprint response has empty serviceId or customerUserId")
                return@withContext
            }

            // Register device with the found service and customer
            val deviceDataStore = deviceManager.registerDevice(
                serviceId = fingerprintResponse.serviceId,
                customerUserId = fingerprintResponse.customerUserId
            )
            Log.d(TAG, "Device registered successfully. ID: ${deviceDataStore.deviceId}")
        } catch (e: Exception) {
            Log.e(TAG, "Error during fingerprint check", e)
        }
    }
}
