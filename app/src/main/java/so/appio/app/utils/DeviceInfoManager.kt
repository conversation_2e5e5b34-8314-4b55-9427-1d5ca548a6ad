package so.appio.app.utils

import android.Manifest
import android.annotation.SuppressLint
import android.app.NotificationManager
import android.content.Context
import android.content.pm.PackageManager
import android.os.Build
import android.provider.Settings
import android.util.Log
import androidx.core.content.ContextCompat
//import android.util.DisplayMetrics
//import android.os.Environment
//import android.os.StatFs

/**
 * DeviceInfoManager - Collects comprehensive device information
 *
 * This class provides methods to gather various device properties including:
 * - Basic device info (name, model, OS version)
 * - Hardware specifications
 * - System settings and permissions
 * - Storage and battery information
 * - Network connectivity details
 */
class DeviceInfoManager(private val context: Context) {

    companion object {
        private const val TAG = "LOG:DeviceInfoManager"
    }

    /**
     * Data class representing all collected device information
     */
    data class DeviceInfo(
        val name: String,
        val osVersion: String,
        val deviceIdentifier: String,
        val manufacturerModel: String,
        val notificationsEnabled: <PERSON>olean,

        // Additional useful fields
//        val brand: String,
//        val hardware: String,
//        val apiLevel: Int,
//        val screenDensity: String,
//        val screenResolution: String,
//        val totalStorageGB: Double,
//        val appVersion: String,
//        val appVersionCode: Long,
//        val appInstallTimestamp: Long,
    )

    /**
     * Collects all device information and returns as DeviceInfo object
     */
    fun collectDeviceInfo(): DeviceInfo {
        Log.d(TAG, "Starting device info collection...")

        return DeviceInfo(
            name = getDeviceName(),
            osVersion = getOsVersion(),
            deviceIdentifier = getDeviceIdentifier(),
            manufacturerModel = "${Build.MANUFACTURER}┼${Build.MODEL}",
            notificationsEnabled = areNotificationsEnabled(),

            // Additional
//            brand = Build.BRAND,
//            hardware = Build.HARDWARE,
//            apiLevel = Build.VERSION.SDK_INT,
//            screenDensity = getScreenDensity(),
//            screenResolution = getScreenResolution(),
//            totalStorageGB = getTotalStorageGB(),
//            appVersion = getAppVersion(),
//            appVersionCode = getAppVersionCode(),
//            appInstallTimestamp = getAppInstallTimestamp(context),
        )
    }

    /**
     * Logs all device information for debugging purposes
     */
    fun logDeviceInfo() {
        val deviceInfo = collectDeviceInfo()
        Log.d(TAG, "=== DEVICE INFORMATION ===")
        Log.d(TAG, " ├ Name: ${deviceInfo.name}")
        Log.d(TAG, " ├ OS Version: ${deviceInfo.osVersion}")
        Log.d(TAG, " ├ Device Identifier: ${deviceInfo.deviceIdentifier}")
        Log.d(TAG, " ├ Manufacturer & Model: ${deviceInfo.manufacturerModel}")
        Log.d(TAG, " ├ Notifications: ${deviceInfo.notificationsEnabled}")
        Log.d(TAG, " ├──────────────────────────────")
//        Log.d(TAG, " ├ Brand: ${deviceInfo.brand}")
//        Log.d(TAG, " ├ Hardware: ${deviceInfo.hardware}")
//        Log.d(TAG, " ├ API Level: ${deviceInfo.apiLevel}")
//        Log.d(TAG, " ├ Screen Density: ${deviceInfo.screenDensity}")
//        Log.d(TAG, " ├ Screen Resolution: ${deviceInfo.screenResolution}")
//        Log.d(TAG, " ├ Total Storage: ${deviceInfo.totalStorageGB} GB")
//        Log.d(TAG, " └ App Version: ${deviceInfo.appVersion} (${deviceInfo.appVersionCode})")
//        Log.d(TAG, "========================")
    }

    // Private methods for collecting individual pieces of information

    private fun getDeviceName(): String {
        return try {
            // Try to get user-defined device name first
            Settings.Global.getString(context.contentResolver, "device_name")
                ?: Settings.Secure.getString(context.contentResolver, "bluetooth_name")
                ?: Build.MODEL // Fallback to model name
        } catch (e: Exception) {
            Log.w(TAG, "Could not get device name", e)
            Build.MODEL
        }
    }

    private fun getOsVersion(): String {
        return "${Build.VERSION.RELEASE} (API ${Build.VERSION.SDK_INT})"
    }

    @SuppressLint("HardwareIds")
    private fun getDeviceIdentifier(): String {
        return try {
            Settings.Secure.getString(context.contentResolver, Settings.Secure.ANDROID_ID)
        } catch (e: Exception) {
            Log.w(TAG, "Could not get Android ID", e)
            "unknown"
        }
    }

    private fun areNotificationsEnabled(): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            // For Android 13+, check both permission AND notification status
            val hasPermission = ContextCompat.checkSelfPermission(
                context,
                Manifest.permission.POST_NOTIFICATIONS
            ) == PackageManager.PERMISSION_GRANTED

            val notificationsEnabled = try {
                val notificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
                notificationManager.areNotificationsEnabled()
            } catch (e: Exception) {
                Log.w(TAG, "Could not check notification status", e)
                false
            }

            // Both permission must be granted AND notifications must be enabled
            hasPermission && notificationsEnabled
        } else {
            // For Android < 13, check if notifications are enabled at system level
            val notificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            notificationManager.areNotificationsEnabled()
        }
    }

    fun getScreenResolution(): String {
        return try {
            val metrics = context.resources.displayMetrics
            "${metrics.widthPixels}x${metrics.heightPixels}"
        } catch (e: Exception) {
            Log.w(TAG, "Could not get screen resolution", e)
            "unknown"
        }
    }

//    private fun getScreenDensity(): String {
//        return try {
//            val metrics = context.resources.displayMetrics
//            when (metrics.densityDpi) {
//                DisplayMetrics.DENSITY_LOW -> "ldpi (120dpi)"
//                DisplayMetrics.DENSITY_MEDIUM -> "mdpi (160dpi)"
//                DisplayMetrics.DENSITY_HIGH -> "hdpi (240dpi)"
//                DisplayMetrics.DENSITY_XHIGH -> "xhdpi (320dpi)"
//                DisplayMetrics.DENSITY_XXHIGH -> "xxhdpi (480dpi)"
//                DisplayMetrics.DENSITY_XXXHIGH -> "xxxhdpi (640dpi)"
//                else -> "${metrics.densityDpi}dpi"
//            }
//        } catch (e: Exception) {
//            Log.w(TAG, "Could not get screen density", e)
//            "unknown"
//        }
//    }
//
//    private fun getTotalStorageGB(): Double {
//        return try {
//            val stat = StatFs(Environment.getDataDirectory().path)
//            val totalBytes = stat.totalBytes
//            totalBytes / (1024.0 * 1024.0 * 1024.0) // Convert to GB
//        } catch (e: Exception) {
//            Log.w(TAG, "Could not get total storage", e)
//            0.0
//        }
//    }
//
//    private fun getAppVersion(): String {
//        return try {
//            val packageInfo = context.packageManager.getPackageInfo(context.packageName, 0)
//            packageInfo.versionName ?: "unknown"
//        } catch (e: Exception) {
//            Log.w(TAG, "Could not get app version", e)
//            "unknown"
//        }
//    }
//
//    private fun getAppVersionCode(): Long {
//        return try {
//            val packageInfo = context.packageManager.getPackageInfo(context.packageName, 0)
//            packageInfo.longVersionCode
//        } catch (e: Exception) {
//            Log.w(TAG, "Could not get app version code", e)
//            -1L
//        }
//    }
//
//    private fun getAppInstallTimestamp(context: Context): Long {
//        return try {
//            val packageInfo = context.packageManager.getPackageInfo(context.packageName, 0)
//            packageInfo.firstInstallTime // returns time in millis since epoch
//        } catch (e: Exception) {
//            0L
//        }
//    }
}
