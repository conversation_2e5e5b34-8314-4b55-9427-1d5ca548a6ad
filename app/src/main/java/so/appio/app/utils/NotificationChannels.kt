package so.appio.app.utils

import android.app.NotificationChannel
import android.app.NotificationManager
import android.content.Context
import android.media.RingtoneManager
import android.util.Log
import androidx.core.content.ContextCompat
import so.appio.app.MainActivity.Companion.TAG
import so.appio.app.R

object NotificationChannels {

    fun createNotificationChannels(context: Context) {
        createDefaultNotificationChannel(context)

        // TODO: For each service, register:
        val serviceId = "svc_00000000000000000000000000"
        val serviceTitle = "Awesome Service"
        val channelId = "fcm_channel_$serviceId"

        createChannel(context, channelId, serviceTitle)
    }

    private fun createDefaultNotificationChannel(context: Context) {
        createChannel(context, context.getString(R.string.default_notification_channel_id), "Appio")
    }

    private fun createChannel(context: Context, channelId: String, name: String) {
        val notificationManager: NotificationManager =
            ContextCompat.getSystemService(context, NotificationManager::class.java) as NotificationManager

        val existingChannel = notificationManager.getNotificationChannel(channelId)
        if (existingChannel != null) {
            Log.d(TAG, "Notification channel already exists: $channelId")
            return
        }

        val channelName = "$name Notifications"
        val channelDescription = "Notifications from $name"
        val channelImportance = NotificationManager.IMPORTANCE_HIGH
        val channel = NotificationChannel(channelId, channelName, channelImportance).apply {
            description = channelDescription

            // sound, vibration, showBadge: are on by default

            // lockscreenVisibility will be set by individual notification. default value: VISIBILITY_PRIVATE
        }

        notificationManager.createNotificationChannel(channel)
        Log.d(TAG, "Notification channel created. $name: $channelId")
    }
}