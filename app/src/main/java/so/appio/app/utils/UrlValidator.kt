package so.appio.app.utils

import java.net.URI
import java.net.URLDecoder

/**
 * Data class to hold parsed URL parameters
 */
data class ParsedUrlParams(
    val serviceId: String,
    val customerUserId: String?
)

/**
 * Sealed class to represent URL validation results
 */
sealed class UrlValidationResult {
    data class Success(val params: ParsedUrlParams) : UrlValidationResult()
    data class Error(val message: String) : UrlValidationResult()
}

/**
 * Utility class for validating and parsing Appio URLs
 *
 * Supports two URL formats:
 * 1. Web URL: https://app.appio.so/?s=X&u=Y or https://app.appio.so/android/?s=X&u=Y or https://app.appio.so/ * /?s=X&u=Y
 * 2. Deep link: appio://appio/?service=X&user=Y
 */
object UrlValidator {
    
    private const val WEB_SCHEME = "https"
    private const val WEB_HOST = "app.appio.so"
    private const val DEEP_LINK_SCHEME = "appio"
    private const val DEEP_LINK_HOST = "appio"
    
    // Parameter names for web URL format
    private const val WEB_SERVICE_PARAM = "s"
    private const val WEB_USER_PARAM = "u"
    
    // Parameter names for deep link format
    private const val DEEP_LINK_SERVICE_PARAM = "service"
    private const val DEEP_LINK_USER_PARAM = "user"
    
    /**
     * Validates and parses a URL to extract serviceId and customerUserId
     *
     * @param url The URL string to validate and parse
     * @return UrlValidationResult containing either success with parsed params or error with message
     */
    fun validateAndParseUrl(url: String): UrlValidationResult {
        if (url.isBlank()) {
            return UrlValidationResult.Error("URL cannot be empty")
        }

        return try {
            val uri = URI(url)

            when {
                isWebUrl(uri) -> parseWebUrl(uri)
                isDeepLinkUrl(uri) -> parseDeepLinkUrl(uri)
                else -> UrlValidationResult.Error("Invalid URL format. Expected https://app.appio.so or appio://appio")
            }
        } catch (e: Exception) {
            UrlValidationResult.Error("Failed to parse URL: ${e.message}")
        }
    }
    
    /**
     * Checks if the URI is a valid web URL format
     */
    private fun isWebUrl(uri: URI): Boolean {
        return uri.scheme?.lowercase() == WEB_SCHEME &&
               uri.host?.lowercase() == WEB_HOST
    }

    /**
     * Checks if the URI is a valid deep link format
     */
    private fun isDeepLinkUrl(uri: URI): Boolean {
        return uri.scheme?.lowercase() == DEEP_LINK_SCHEME &&
               uri.host?.lowercase() == DEEP_LINK_HOST
    }
    
    /**
     * Parses query parameters from URI
     */
    private fun parseQueryParameters(uri: URI): Map<String, String> {
        val query = uri.query ?: return emptyMap()
        return query.split("&")
            .mapNotNull { param ->
                val parts = param.split("=", limit = 2)
                if (parts.size == 2) {
                    URLDecoder.decode(parts[0], "UTF-8") to URLDecoder.decode(parts[1], "UTF-8")
                } else null
            }
            .toMap()
    }

    /**
     * Parses web URL format: https://app.appio.so/?s=X&u=Y
     */
    private fun parseWebUrl(uri: URI): UrlValidationResult {
        val queryParams = parseQueryParameters(uri)
        val serviceId = queryParams[WEB_SERVICE_PARAM]
        val customerUserId = queryParams[WEB_USER_PARAM]

        return if (serviceId.isNullOrBlank()) {
            UrlValidationResult.Error("Missing required parameter 's' (serviceId)")
        } else {
            UrlValidationResult.Success(
                ParsedUrlParams(
                    serviceId = serviceId,
                    customerUserId = customerUserId?.takeIf { it.isNotBlank() }
                )
            )
        }
    }

    /**
     * Parses deep link format: appio://appio/?service=X&user=Y
     */
    private fun parseDeepLinkUrl(uri: URI): UrlValidationResult {
        val queryParams = parseQueryParameters(uri)
        val serviceId = queryParams[DEEP_LINK_SERVICE_PARAM]
        val customerUserId = queryParams[DEEP_LINK_USER_PARAM]

        return if (serviceId.isNullOrBlank()) {
            UrlValidationResult.Error("Missing required parameter 'service' (serviceId)")
        } else {
            UrlValidationResult.Success(
                ParsedUrlParams(
                    serviceId = serviceId,
                    customerUserId = customerUserId?.takeIf { it.isNotBlank() }
                )
            )
        }
    }
    
    /**
     * Convenience method to check if a URL is valid without parsing
     */
    fun isValidUrl(url: String): Boolean {
        return when (validateAndParseUrl(url)) {
            is UrlValidationResult.Success -> true
            is UrlValidationResult.Error -> false
        }
    }

    /**
     * Checks if a URL is valid for opening in a browser (general URL validation)
     * This is more permissive than validateAndParseUrl which only accepts Appio URLs
     */
    fun isValidBrowserUrl(url: String): Boolean {
        if (url.isBlank()) return false

        return try {
            val uri = URI(url)
            // Check if it has a valid scheme and host
            val scheme = uri.scheme?.lowercase()
            val host = uri.host

            // Accept http, https, and other common schemes
            (scheme == "http" || scheme == "https" || scheme == "ftp" || scheme == "ftps") &&
            !host.isNullOrBlank()
        } catch (e: Exception) {
            false
        }
    }

    /**
     * Extracts the main domain from a URL (without scheme, path, or query params)
     * Returns null if the URL is invalid
     */
    fun extractMainDomain(url: String): String? {
        return try {
            val uri = URI(url)
            uri.host
        } catch (e: Exception) {
            null
        }
    }
}
