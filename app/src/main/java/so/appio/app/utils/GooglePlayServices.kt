package so.appio.app.utils

import android.content.Context
import android.util.Log
import com.google.android.gms.common.ConnectionResult
import com.google.android.gms.common.GoogleApiAvailability
import com.google.firebase.FirebaseApp
import com.google.firebase.messaging.FirebaseMessaging

/**
 * Utility class for Google Play Services and Firebase initialization
 * Provides fallback handling when GMS is not available (e.g., in emulators)
 */
object GooglePlayServices {
    private const val TAG = "LOG:GooglePlayServices"

    /**
     * Check if Google Play Services is available on this device
     * @param context Application context
     * @return true if Google Play Services is available and up to date
     */
    fun isGooglePlayServicesAvailable(context: Context): <PERSON><PERSON>an {
        return try {
            val googleApiAvailability = GoogleApiAvailability.getInstance()
            val resultCode = googleApiAvailability.isGooglePlayServicesAvailable(context)

            when (resultCode) {
                ConnectionResult.SUCCESS -> {
                    Log.d(TAG, "Google Play Services is available and up to date")
                    true
                }
                ConnectionResult.SERVICE_VERSION_UPDATE_REQUIRED -> {
                    Log.w(TAG, "Google Play Services needs to be updated")
                    false
                }
                ConnectionResult.SERVICE_DISABLED -> {
                    Log.w(TAG, "Google Play Services is disabled")
                    false
                }
                ConnectionResult.SERVICE_MISSING -> {
                    Log.w(TAG, "Google Play Services is not installed")
                    false
                }
                else -> {
                    Log.w(TAG, "Google Play Services availability check failed with code: $resultCode")
                    false
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error checking Google Play Services availability", e)
            false
        }
    }

    /**
     * Initialize Firebase if Google Play Services is available
     * @param context Application context
     */
    fun initializeFirebase(context: Context) {
        try {
            // Check if Google Play Services is available
            if (!isGooglePlayServicesAvailable(context)) {
                Log.w(TAG, "Google Play Services not available, skipping Firebase initialization")
                return
            }

            // Initialize Firebase
            FirebaseApp.initializeApp(context)
            FirebaseMessaging.getInstance().isAutoInitEnabled = true
            Log.d(TAG, "Firebase initialized successfully")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to initialize Firebase", e)
        }
    }

    /**
     * Check if Firebase is properly initialized and configured
     * @return true if Firebase is ready for use
     */
    fun isFirebaseReady(): Boolean {
        return try {
            val app = FirebaseApp.getInstance()
            val projectId = app.options.projectId
            val isReady = !projectId.isNullOrEmpty()
            Log.d(TAG, "Firebase ready check: $isReady")
            isReady
        } catch (e: Exception) {
            Log.w(TAG, "Firebase not ready: ${e.message}")
            false
        }
    }
}
